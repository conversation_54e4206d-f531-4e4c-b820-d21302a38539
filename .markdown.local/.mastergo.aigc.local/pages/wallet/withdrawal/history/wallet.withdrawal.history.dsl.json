{"dsl": {"styles": {"paint_3:24017": {"value": ["#F7F8FA"], "token": "填充 Fill/填充fill-1"}, "paint_3:01045": {"value": ["#FFFFFF"], "token": "填充 Fill/Container 容器背景色"}, "paint_1:06": {"value": ["#000000"]}, "font_3:9436": {"value": {"family": "Source Han Sans CN", "size": 16.5, "decoration": "none", "case": "none", "lineHeight": "42", "letterSpacing": "auto"}}, "paint_3:9443": {"value": ["#333333"]}, "effect_1:12": {"value": []}, "paint_3:22622": {"value": []}, "paint_3:9477": {"value": []}, "paint_3:9736": {"value": ["#1D2129"], "token": "文字 Text/文字-5-基础  Grey 10"}, "font_3:08006": {"value": {"family": "PingFang SC", "size": 16, "style": "{\"fontStyle\":\"Bold\",\"opsz\":\"auto\"}", "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "16/Bold"}, "effect_408:039881": {"value": []}, "paint_98:100269": {"value": ["#FFFFFF"]}, "paint_3:46804": {"value": ["rgba(153, 153, 153, 0.2)"]}, "font_3:01136": {"value": {"family": "PingFangSC-Medium", "size": 14, "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "14/Medium"}, "paint_3:00274": {"value": ["#86909C"], "token": "文字 Text/文字-3-附加信息-Grey 6"}, "paint_3:08543": {"value": ["#F53F3F"], "token": "危险 Danger/危险-6-基础"}, "paint_3:10527": {"value": ["#FFECE8"], "token": "危险 Danger/危险-1-更浅"}, "font_3:00273": {"value": {"family": "PingFangSC-Medium", "size": 12, "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "12/Medium"}, "paint_3:08039": {"value": ["#E5E6EB"], "token": "线条 Line/线条line-1-基础"}, "effect_10:27593": {"value": []}, "paint_3:07811": {"value": ["rgba(0, 0, 0, 0.9)"], "token": "特殊 Special Color/image-preview-mask-bg"}}, "nodes": [{"type": "FRAME", "id": "450:81808", "name": "提现记录-<PERSON><PERSON>", "layoutStyle": {"width": 375, "height": 840, "relativeX": 0, "relativeY": 0}, "fill": "paint_3:24017", "children": [{"type": "INSTANCE", "id": "450:82563", "layoutStyle": {"width": 375, "height": 84}, "flexShrink": 1, "fill": "paint_3:01045", "componentInfo": {"name": "导航栏-基础组件"}}, {"type": "FRAME", "id": "450:81809", "name": "页面内容", "layoutStyle": {"width": 375, "height": 722, "relativeY": 84}, "flexShrink": 1, "children": [{"type": "FRAME", "id": "450:81810", "name": "提现详情", "layoutStyle": {"width": 375, "height": 722}, "flexShrink": 1, "children": [{"type": "FRAME", "id": "450:81811", "name": "提现中", "layoutStyle": {"width": 355, "height": 185, "relativeX": 10, "relativeY": 10}, "flexShrink": 1, "fill": "paint_98:100269"}, {"type": "FRAME", "id": "450:81849", "name": "提现成功", "layoutStyle": {"width": 355, "height": 245, "relativeX": 10, "relativeY": 205}, "flexShrink": 1, "fill": "paint_98:100269"}, {"type": "FRAME", "id": "450:81900", "name": "提现失败", "layoutStyle": {"width": 355, "height": 218, "relativeX": 10, "relativeY": 460}, "flexShrink": 1, "fill": "paint_98:100269"}]}]}]}]}, "componentDocumentLinks": [], "rules": ["token filed must be generated as a variable (colors, shadows, fonts, etc.) and the token field must be displayed in the comment", "\n            componentDocumentLinks is a list of frontend component documentation links used in the DSL layer, designed to help you understand how to use the components.\n            When it exists and is not empty, you need to use mcp__getComponentLink in a for loop to get the URL content of all components in the list, understand how to use the components, and generate code using the components.\n            For example: \n              ```js  \n                const componentDocumentLinks = [\n                  'https://example.com/ant/button.mdx',\n                  'https://example.com/ant/button.mdx'\n                ]\n                for (const url of componentDocumentLinks) {\n                  const componentLink = await mcp__getComponentLink(url);\n                  console.log(componentLink);\n                }\n              ```\n          "]}